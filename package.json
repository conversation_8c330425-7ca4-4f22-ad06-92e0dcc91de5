{"name": "tontine-websites", "version": "1.0.0", "scripts": {"setup": "npm i && cd sanity-studio && npm i", "format": "biome format", "format:fix": "biome format --write", "lint": "biome lint", "lint:fix": "biome lint --write", "format-lint": "biome check", "format-lint:fix": "biome check --write", "check-types:sanity": "tsc --project sanity-studio --noEmit --pretty", "check-types:cypress": "tsc --project cypress --noEmit --pretty", "check-types:tontine": "tsc --project tontine --noEmit --pretty", "check-types:all": "npm run check-types:sanity && npm run check-types:cypress && npm run check-types:tontine", "dev-tontine": "npm run check-types:tontine && npm run lint && next dev tontine -p 3000 --turbo", "dev-tontine:ft": "next dev tontine -p 3000 --turbo", "dev-tontine:f": "next dev tontine -p 3000", "start-tontine": "next start tontine -p 3000", "dull-build": "cross-env BUILD_ENV=true npx next build tontine", "build-tontine": "cross-env BUILD_ENV=true npx next build tontine", "build-stats": "cross-env ANALYZE=true npm run build-tontine", "cypress": "npx cypress open -C cypress/cypress.config.ts", "cypress:headless": "npx cypress run -C cypress/cypress.config.ts", "e2e": "cross-env NEXT_PUBLIC_CYPRESS_RUN=true npx start-server-and-test dev-tontine:ft http://localhost:3000/ cypress", "e2e:headless": "cross-env NEXT_PUBLIC_CYPRESS_RUN=true npx start-server-and-test start-tontine http://localhost:3000/ cypress:headless", "tc-report:tontine": "npx type-coverage --project tontine/tsconfig.json", "tc-report:sanity": "npx type-coverage --project sanity-studio/tsconfig.json", "postbuild": "./remove-sourcemaps.sh"}, "dependencies": {"@mux/mux-video-react": "^0.24.4", "@sanity/asset-utils": "^2.2.1", "@sanity/client": "6.28.4", "@sanity/icons": "^3.7.0", "@sentry/nextjs": "^9.10.1", "@tailwindcss/postcss": "^4.0.17", "axios": "1.8.4", "axios-retry": "^4.5.0", "clsx": "^2.1.1", "cookies-next": "^5.1.0", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "mixpanel-browser": "^2.62.0", "next": "15.2.4", "next-sanity": "9.9.6", "next-share": "0.27.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-fast-marquee": "^1.6.5", "react-turnstile": "^1.1.4", "rss": "^1.2.2", "sharp": "^0.33.5", "tailwind-merge": "^3.0.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@netlify/plugin-nextjs": "5.10.1", "@next/bundle-analyzer": "15.2.4", "@percy/cli": "1.30.7", "@percy/cypress": "3.1.5", "@testing-library/cypress": "10.0.3", "@types/mixpanel-browser": "^2.54.0", "@types/node": "22.13.14", "@types/react": "^19.0.12", "@types/rss": "^0.0.32", "cross-env": "7.0.3", "cssnano": "7.0.6", "cypress": "^14.2.1", "postcss": "^8.5.3", "schema-dts": "^1.1.5", "start-server-and-test": "2.0.11", "tailwindcss": "^4.0.17", "type-coverage": "^2.29.7", "typescript": "^5.8.2"}, "release": {"branches": ["production"]}, "typeCoverage": {"ignoreFiles": ["cypress/**/*", "coverage-ts/**/*", "**/*.json", "**/public/**/*", "**/.next/**/*", "**/out/**", "**/.sanity/**/*", "**/dist/**", "**/**.js", "**/**.jsx", "**/*.config.ts", "**/*.config.js"], "strict": true, "atLeast": 9, "fileCounts": true}}