import path from 'path'
import { type ClassValue, clsx } from 'clsx'
import { toPlainText } from 'next-sanity'
import type { ReadonlyRequestCookies } from 'next/dist/server/web/spec-extension/adapters/request-cookies'
import type { ChangeEvent } from 'react'
import { twMerge } from 'tailwind-merge'
import { fetchGeorgeYield, fetchReturnRate } from '../app/api/dynamic-metrics'
import { CONSTANTS } from '../data-resource/constants'
import strings from '../data-resource/strings.json'
import { logServerless } from '../serverless/ApiUtilFunctions'
import {
  buildContext,
  cookieWebsiteId,
  isCypress,
  isProd,
  prodWebsiteID,
} from '../serverless/keys'
import type {
  BreakpointsType,
  LanguagesType,
  LinkSelectorProps,
  ThrottledFunction,
  VideoMetadataType,
} from '../types/common.types'
import type { ComponentPlacement } from '../types/component.types'
import type { ContentPost } from '../types/sections/content-section.types'
import type { CTACardPosition } from '../types/sections/cta.types'
import type { QuestionAndAnswer } from '../types/sections/faq-section.types'
import type {
  LocalizedContentType,
  LocalizedStringContentType,
  TestimonialCardType,
  TestimonialType,
} from '../types/sections/section.types'

/**
 * Returns an object with the right props to be passed to the TimeAndDateLayout component
 * as well as default styles for the icon and text.
 */
export const timeAndDateProps = ({
  postDate,
  time,
  article,
}: {
  postDate?: string
  time?: string | number
  article?: ContentPost
}) => {
  return {
    blockDate: convertDateToClientLocale(postDate ?? ''),
    duration: article ? getReadWatchTimeString(article) : time,
    iconProps: { className: 'w-5.5 h-5.5 relative' },
    textProps: {
      className: 'text-sm',
    },
  }
}

/** A utility function that takes a localized string or content object and returns a plain text version of it. */
export const convertToPlainText = ({
  value,
  language = 'en',
}: {
  value?: LocalizedContentType | LocalizedStringContentType
  language?: LanguagesType
}) => {
  if (!value) return ''

  if (typeof value === 'string') return value

  const content = value?.[language] ?? value.en

  if (Array.isArray(content)) {
    return toPlainText(content)
  }

  return content ?? ''
}

/** Merges class names using clsx and tailwind-merge. */
export const cn = (...inputs: Array<ClassValue>) => twMerge(clsx(inputs))

/** Given a placement type, returns the appropriate Tailwind CSS classes for positioning. */
export const getPlacementClasses = ({
  placement,
  offsetY,
  offsetX,
}: ComponentPlacement) => {
  switch (placement) {
    case 'left':
      return cn('right-[unset] left-0', offsetX && offsetX)
    case 'center':
      return cn(
        '-translate-x-1/2 left-1/2 transform',
        offsetX && offsetX,
        offsetY && offsetY
      )
    case 'right':
      return cn('right-0 left-[unset]', offsetX && offsetX)
    default:
      return ''
  }
}

/** Returns the website ID from the cookies.
 * If the app is in production, the website ID is set to the production website ID.
 * If the app is not in production, the website ID is retrieved from cookies.
 * If the cookie is not found, the function returns undefined.
 */
export const getWebsiteId = async (
  cookies: () => Promise<ReadonlyRequestCookies>
) => {
  const nextCookies = isProd ? undefined : await cookies()
  return isProd ? prodWebsiteID : nextCookies?.get(cookieWebsiteId)?.value
}

/** Splits a given string by a given length.
 * If the string is shorter than or equal to the given length, it will be returned as a single element array.
 * If the string is longer than the given length, it will be split into an array of strings, each with a maximum length of the given length.
 * The string will be split at the nearest whitespace character, or at the end of the string if no whitespace character is found.
 * If the string contains no whitespace characters, the string will be split into multiple strings of the given length.
 */
export const splitStringByLength = ({
  input,
  length,
}: {
  input: string
  length: number
}) => {
  if (!input || input.length <= length) return [input]

  const regex = new RegExp(`\\S.{0,${length - 1}}(?=\\s|$)|.{1,${length}}`, 'g')
  return input.match(regex) || [input]
}

/**
 * Returns a debounced function of the passed in function
 */
export const debounce = <T extends Array<unknown>>(
  func: (...args: T) => void,
  timeout = 300
) => {
  let timer: NodeJS.Timeout

  return (...args: T): void => {
    clearTimeout(timer)
    timer = setTimeout(() => {
      func.apply(this, args)
    }, timeout)
  }
}

/**
 *  Returns a throttled function of the passed in function
 */
export const throttle = <T extends Array<unknown>>(
  func: (...args: T) => void,
  timeout = 300
): ThrottledFunction<T> => {
  let timer: NodeJS.Timeout | null = null

  return (...args: T): void => {
    if (!timer) {
      func.apply(this, args)
      timer = setTimeout(() => {
        timer = null
      }, timeout)
    }
  }
}

/**
 * Detects a device type depending on the screen size.
 *
 * @note This is NOT an accurate way to detect user is using a certain device.
 */
export const detectDeviceType = (): 'desktop' | 'tablet' | 'mobile' => {
  let deviceType: 'desktop' | 'tablet' | 'mobile' = 'desktop'

  if (typeof window !== 'undefined') {
    if (window?.innerWidth >= 992) {
      // Desktops and laptops typically have screens wider than 992px
      deviceType = 'desktop'
    } else if (window?.innerWidth >= 768 && window?.innerWidth < 992) {
      // Tablets typically have screens between 768px and 991px wide
      deviceType = 'tablet'
    } else {
      // Mobile devices typically have screens narrower than 767px
      deviceType = 'mobile'
    }
  }
  return deviceType
}

export const generateVideoSchema = ({
  title,
  subtitle,
  video,
  videoThumbnail,
  pageDomain,
}: VideoMetadataType) => {
  const parsedTitle = Array.isArray(title?.en) ? toPlainText(title?.en) : title
  const parsedDescription = Array.isArray(subtitle?.en)
    ? toPlainText(subtitle?.en)
    : title

  const videoProps = {
    '@context': 'https://schema.org',
    '@type': 'VideoObject',
    name: parsedTitle,
    description: parsedDescription,
    duration: formatTimeHoursMinutesSecondsForWebschema(
      Number(video?.duration.toFixed(0))
    ),
    isLiveBroadcast: false,
    startDate: video?.createdAt,
    endDate: video?.createdAt,
    videoQuality: video?.quality,
    width: `${video?.width}`,
    height: `${video?.height}`,
    contentUrl: getMuxContentUrl(video?.playbackId ?? ''),
    thumbnailUrl: path.join(
      pageDomain ?? '',
      convertSanityURLToNext(videoThumbnail?.url ?? '')
    ),
    uploadDate: video?.createdAt,
    dateModified: video?.updatedAt,
    dateCreated: video?.createdAt,
  }
  return videoProps
}

/** Creates a mux playback url using the playback id, defaults to explainer video. */
export const getMuxContentUrl = (playbackId: string) => {
  if (playbackId) {
    return `https://stream.mux.com/${playbackId}.m3u8`
  }
  return `https://stream.mux.com/${strings.EXPLAINER_VIDEO_PLAYBACK_ID}.m3u8`
}

/** Gets the possible numbers in range */
export const numberWithinRange = (
  number: number,
  min: number,
  max: number
): number => Math.min(Math.max(number, min), max)

/**
 * Extracts the dimensions from a file name.
 *
 * The function assumes that the `filename` is in a specific format where
 * the dimensions are specified as 'widthxheight' and are located between
 * the last '-' and the first '.' in the filename. (e.g., `image-1920x1080.webp`)
 */
export function extractDimensions(fileName: string) {
  if (!fileName) return null

  const regex = /-(\d+)x(\d+)\./
  const match = fileName.match(regex)

  if (match) {
    const width = Number(match[1])
    const height = Number(match[2])

    if (width && height) {
      return { width, height }
    }
  }

  return null
}

/** Replaces slashes "/" with "%2F" and ":" with "%3A" */
export function convertToSafeURL(url: string) {
  if (url) return url.replace(/:/g, '%3A').replace(/\//g, '%2F')
  return url
}

/** Convert sanity image url to optimized next url */
export function convertSanityURLToNext(imageURL: string): string {
  const dimensions = extractDimensions(imageURL)

  const width = Math.min(
    dimensions?.width ?? 0,
    CONSTANTS.RESIZE_IMAGE_SIZES.max.width
  )

  // Replace :// with % and / with %
  const encodedUrl = convertToSafeURL(imageURL)

  // Construct the final URL string
  const convertedURL = `_next/image/?url=${encodedUrl}&amp;w=${width}&amp;q=${CONSTANTS.IMAGE_QUALITY.MINIMUM}`

  return convertedURL
}

/** Replaced white spaces with custom string or with "-" */
export function replaceWhitespace({
  str,
  replaceWith = '-',
}: {
  str: string
  replaceWith?: string
}) {
  if (str) return str?.replace(/\s+/g, `${replaceWith}`)
  return str
}

/** Replaced white spaces with custom string or with "-" */
export function replaceString({
  str,
  replace,
  replaceWith,
}: {
  str: string
  replace: string
  replaceWith: string
}) {
  if (str) return str?.replace(new RegExp(replace, 'g'), `${replaceWith}`)
  return str
}

/** Cleans a URL by removing the protocol (http:// or https://) and the 'www.' prefix.
 */
export function cleanPageDomain(url: string) {
  if (url) return url?.replace(/(https?:\/\/)?(www\.)?/, '')
  return url
}

/**
 * Joins an array of segments into a single path string, with optional leading and ending trailing slash.
 *
 * - `segments` - An array of segments to join into a path.
 * - `leadingSlash` - Whether to include a leading slash in the path. Defaults to false.
 * - `endsWithSlash` - Whether to include a trailing slash at the end of the path. Defaults to false.
 */
export const generatePathWithTrailingSlash = ({
  segments,
  leadingSlash = false,
  endsWithSlash = false,
}: {
  segments: Array<string>
  leadingSlash?: boolean
  endsWithSlash?: boolean
}): string => {
  const route = segments?.filter(Boolean).join('/')

  return `${leadingSlash ? '/' : ''}${route}${endsWithSlash ? '/' : ''}`
}

/** Generates a slug based on the provided slug data. */
export const generateSlug = (slugData: LinkSelectorProps) => {
  if (slugData?.linkType === 'custom') {
    return slugData?.customParamsButton && slugData?.customParams
      ? generatePathWithTrailingSlash({
          segments: [slugData?.customLink, `?${slugData?.customParams}`],
        })
      : slugData?.customLink
  }

  return generatePathWithTrailingSlash({
    segments: [
      slugData?.pageSlug?.current,
      slugData?.sectionSlug?.current && `#${slugData?.sectionSlug?.current}`,
    ].filter(Boolean),
  })
}

/**
 * Generates an image path based on the provided path.
 * - `imagePath` - The path of the image.
 */
export function getImagePath({ imagePath }: { imagePath: string }) {
  try {
    if (!imagePath) {
      throw new Error('Image path is undefined.')
    }
    return imagePath
  } catch (error) {
    logServerless({
      error,
      message: 'Failed to create path',
      logLevel: 'error',
    })
  }
  return ''
}

/**
 * Returns an object with the specified attribute name and value if the condition is true, otherwise returns an empty object.
 */
export function getAttribute<T extends string, U>(
  condition: boolean,
  attributeName: T,
  attributeValue: U
) {
  return condition && attributeValue
    ? ({ [attributeName]: attributeValue } as { [K in T]: U })
    : undefined
}

/**
 * Extracts the site name from the given URL.
 */
export function extractSiteName(url: string): string {
  let domain: string = url.replace(/(https?:\/\/)?(www\.)?/, '')
  const parts = domain.split('/')
  domain = parts[0] ? parts[0] : ''
  return domain
}

/**
 * Converts a snake_case string to camelCase.
 */
export const camelCaseString = (string: string): string | undefined => {
  if (!string) return undefined
  // split the string into words
  const words = string?.split('_')
  // capitalize the first letter of each word after the first
  const camelCaseWords = [
    words[0],
    ...words
      .slice(1)
      .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1)),
  ]
  // join the words together into a single string
  const currentCamelCaseString = camelCaseWords.join('')
  // convert the first letter to lowercase
  return (
    currentCamelCaseString.charAt(0).toLowerCase() +
    currentCamelCaseString.slice(1)
  )
}
/**
 * This function converts a number to hours, minutes and seconds And returns a
 * string in pacific time format readable for webschema for ex: PT1H30M44S Which
 * translates to pacific time 1 hour 30 minutes and 44 seconds And if there are
 * no hours it returns a string for ex: PT30M44S And if there are no minutes it
 * returns a string for ex: PT44S
 */
export const formatTimeHoursMinutesSecondsForWebschema = (seconds: number) => {
  const hours: number = Math.floor(seconds / 3600)
  const minutes: number = Math.floor((seconds % 3600) / 60)
  const remainingSeconds: number = seconds % 60
  const formattedTime: string = `PT${hours > 0 ? `${hours}H` : ''}${
    seconds > 59 ? `${minutes}M` : ''
  }${remainingSeconds}S`
  return formattedTime
}

/**
 * converts minutes and seconds to a string
 * @param minutes number of minutes
 * @param seconds number of seconds
 * @returns string of minutes and seconds
 */
export function formatMinutesSeconds(minutes: number, seconds: number): string {
  // Add a leading zero if the number of minutes or seconds is less than 10
  const formattedMinutes = minutes < 10 ? `0${minutes}` : `${minutes}`
  const formattedSeconds = seconds < 10 ? `0${seconds}` : `${seconds}`
  return `${formattedMinutes}:${formattedSeconds}`
}
/**
 * converts seconds to minutes and seconds
 * @param seconds number of seconds
 * @returns string of minutes and seconds
 */
export function convertTimeToMinutesSeconds(seconds: number): string | null {
  if (typeof seconds !== 'number') return null

  const minutes = Math.floor(seconds / 60) // Get the whole number of minutes
  const secondsLeft = seconds % 60 // Get the remaining seconds

  return formatMinutesSeconds(minutes, secondsLeft)
}

/**
 * Checks whether to render read time or watch time or none
 * @param article article object
 */
export function getReadWatchTimeString(post: ContentPost) {
  const time = post?.videoFile?.duration
    ? Math.trunc(post?.videoFile?.duration)
    : post?.readingTime
  if (!time) return undefined

  const readTime = convertTimeToMinutesSeconds(time)
  const readType = post?.videoFile?.duration ? 'watch' : 'read'

  return `${readTime} min ${readType}`
}

/**
 * Function that converts date to client locale
 * @param date date to be converted to client locale
 */
export const convertDateToClientLocale = (date: string) => {
  const convertedDate = new Date(date).toLocaleDateString('en-Us', {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  })
  if (convertedDate === 'Invalid Date') return undefined
  return convertedDate
}

/**
 * Converts date to ISO string
 */
export function convertToISO(dateString?: string) {
  if (dateString) {
    return new Date(dateString).toISOString()
  }
  return new Date().toISOString()
}

/**
 * Pure function that triggers the download of a pdf file
 * @param urlLink url of the pdf file
 */
export const downloadPdf = async (urlLink: string): Promise<void> => {
  const response = await fetch(urlLink)
  const blob = await response.blob()
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  const filename = urlLink.split('/').pop()
  link.download = filename || strings.FILE_PDF
  link.click()
  window.URL.revokeObjectURL(url)
}

/**
 * Replaces empty spaces in the anchor tag text.
 */
export function replaceEmptySpaceInAnchorTag(text?: string): string {
  if (!text) return ''
  return text.replace(/\s/g, '')
}

/**
 * Splits an array into two arrays with approximately equal lengths
 */

export function splitArrayIntoEqualChunks<T>(array: Array<T>) {
  if (array.length === 0) {
    return []
  }
  const midPoint = Math.ceil(array.length / 2)
  const firstHalfArray = array.slice(0, midPoint)
  const secondHalfArray = array.slice(midPoint)
  return [firstHalfArray, secondHalfArray]
}

/**
 * Takes an array, splits it into smaller chunks, and returns another one with
 * the chunks as its items
 */
export function splitAndArrangeArrayIntoColumns(
  cards: Array<TestimonialCardType>,
  numberOfColumns: number
): Array<Array<TestimonialCardType>> {
  if (!Array.isArray(cards) || numberOfColumns < 1) {
    return []
  }

  // Create a copy of cards sorted by content length (longest first)
  const sortedCards = [...cards].sort(
    (a, b) =>
      (convertToPlainText({ value: b?.textContent })?.length || 0) -
      (convertToPlainText({ value: a?.textContent })?.length || 0)
  )

  // Initialize columns array
  const columns: Array<Array<TestimonialCardType>> = Array.from(
    { length: numberOfColumns },
    () => []
  )

  // Distribute cards to balance content length across columns
  sortedCards.forEach((card) => {
    // Find column with the smallest total content length
    const targetColumn = columns.reduce(
      (minIndex, currentColumn, currentIndex) => {
        const currentTotal = currentColumn.reduce(
          (sum, c) =>
            sum + (convertToPlainText({ value: c.textContent })?.length || 0),
          0
        )
        const minTotal =
          columns[minIndex]?.reduce(
            (sum, c) =>
              sum + (convertToPlainText({ value: c.textContent })?.length || 0),
            0
          ) ?? 0
        return currentTotal < minTotal ? currentIndex : minIndex
      },
      0
    )

    columns[targetColumn]?.push(card)
  })

  return columns
}
/**
 * Renames the fields from testimonial data returned from the query to fit the
 * generic TestimonialCardType type
 */
export function renameTestimonialFields(
  testimonialPosts: Array<TestimonialType>
) {
  return testimonialPosts.map((testimonial) => ({
    name: testimonial.testimonialPostReviewerName,
    profession: testimonial.testimonialReviewerProfession,
    workPlace: testimonial.testimonialReviewerCompany,
    linkText: testimonial.reviewerLinkText,
    linkUrl: testimonial.reviewerLinkUrl,
    textContent: testimonial.localizedTestimonialPost,
    image: testimonial.testimonialReviewerImage,
  }))
}

/**
 * This function checks whether the value on email input fields is of a email
 * format. It is utilized for showing error on input field (red border)  when
 * the input value is invalid, and to keep submit button disabled until the input
 * value is of email format. When we check for submit button, or any other part
 * of the form, we have to pass false value for isInputField. Then we only check
 * if the input value matched email format When we check for showing error on
 * the input field itself. We pass true for isInputField. Then the function will
 * check if there is an input value first, and if yes, if it is an email format
 */
export const isEmailValid = (email: string) => {
  // checking with regex whether the input value has "@" symbol and whether it
  // ends with a .com,.net,.co ...etc
  const emailIsInvalid = !email?.match(CONSTANTS.EMAIL_VALIDATION_REGEX)
  // we leave errorMessage false so it does not show error when the input is
  // blank

  if (!email) return { isValid: true, errorMessage: '' }

  if (email && emailIsInvalid) {
    return { isValid: false, errorMessage: strings.ERROR_EMAIL_WRONG_FORMAT }
  }

  return { isValid: true, errorMessage: '' }
}

/**
 *  Function that handles the change of the input fields.
 */
export const handleInputChange = (
  event: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  setValue: (value: string) => void,
  setStopSliderTimer?: (flag: boolean) => void
) => {
  const { name, value } = event.target
  if (name === 'email') {
    if (setStopSliderTimer) {
      setStopSliderTimer(value !== '')
    }
    event.target.value = value.replace(/\s+/g, '')
  } else {
    event.target.value = value.replace(/\s+/g, ' ')
  }
  setValue(event.target.value)
}

/**
 * this function takes argument position and based on that determines the absolute position for the cta Card
 */
export const getCardPosition = (position: CTACardPosition) => {
  switch (position) {
    case 'top-right':
      return /*tw*/ 'top-[7rem] right-[1rem]' /*tw*/
    case 'top-left':
      return /*tw*/ 'top-[7rem] left-[1rem]' /*tw*/
    case 'bottom-right':
      return /*tw*/ 'bottom-[4rem] right-[1rem]' /*tw*/
    case 'bottom-left':
      return /*tw*/ 'bottom-[4rem] left-[1rem]' /*tw*/
    case 'full-bottom-right':
      return /*tw*/ 'bottom-[1rem] right-[1rem]' /*tw*/
    case 'full-bottom-left':
      return /*tw*/ 'bottom-[1rem] left-[1rem]' /*tw*/
    default:
      return /*tw*/ 'bottom-[4rem] right-[1rem]' /*tw*/
  }
}

/**
 * Creates a contact us email body
 */
export function createContactUsEmailBody({
  firstName,
  lastName,
  email,
  selectedOption,
  message,
}: {
  firstName?: string
  lastName?: string
  email?: string
  selectedOption?: string
  message?: string
}) {
  const emailBody = [
    firstName && `Name: ${firstName}`,
    lastName && `Surname: ${lastName}`,
    email && `Email: ${email}`,
    selectedOption && `Reason: ${selectedOption}`,
    message && `Message: ${message}`,
  ]
    .filter(Boolean)
    .join('\n')

  return emailBody
}

/**
 * Checks if a given link is an external or mailto link
 */
export function isExternalLink(link: string) {
  return (
    link?.startsWith('http://') ||
    link?.startsWith('https://') ||
    link?.includes('tel:') ||
    link?.startsWith('mailto:')
  )
}

/**
 * Returns an object with target and rel attributes if the given link is an external link
 */
export function externalLinkAttributes(link: string) {
  return isExternalLink(link)
    ? { target: '_blank', rel: 'nofollow noopener noreferrer' }
    : null
}

/**
 * Formats a given slug or URL to ensure it adheres to the correct href structure.
 *
 * - Adds a leading slash to slugs if missing.
 * - Ensures slugs end with a trailing slash unless it's the root "/".
 * - Preserves fragments (e.g., #subSlug) after the main slug.
 * - Returns full URLs unchanged.
 */
export function formatHref(
  slug: string,
  langOrSkip?: LanguagesType | boolean,
  maybeSkipLocalization?: boolean
): string {
  const lang = typeof langOrSkip === 'string' ? langOrSkip : 'en'
  const skipLocalization =
    typeof langOrSkip === 'boolean'
      ? langOrSkip
      : (maybeSkipLocalization ?? false)

  if (isExternalLink(slug)) {
    return slug
  }

  // Split the slug into main path, query, and hash
  const [beforeHash, hash = ''] = (slug ?? '').split('#')
  const [pathnameRaw, search = ''] = (beforeHash ?? '').split('?')

  // Ensure pathname starts with a slash
  let pathname = pathnameRaw?.startsWith('/') ? pathnameRaw : `/${pathnameRaw}`

  // Add trailing slash if not root
  if (pathname !== '/' && !pathname?.endsWith('/')) {
    pathname += '/'
  }

  // Add localization prefix unless English or skipped
  const localizedPath =
    lang === 'en' || skipLocalization ? pathname : `/${lang}${pathname}`

  // Reassemble final URL
  const queryPart = search ? `?${search}` : ''
  const hashPart = hash ? `#${hash}` : ''

  return `${localizedPath}${queryPart}${hashPart}`
}

/**
 * Filters an array of objects based on a search query and specified searchBy fields.
 *
 * options - The array of objects to be filtered.
 * searchQuery - The search query used for filtering.
 * searchBy - The fields to search within the objects.
 */
export const searchForOption = ({
  options,
  searchQuery,
  searchBy,
}: {
  options: Array<
    Omit<QuestionAndAnswer, 'question' | 'answer'> & {
      question?: string
      answer?: string
    }
  >
  searchQuery: string
  searchBy?: Array<'answer' | 'question' | 'tags'>
}) => {
  if (!searchBy || searchBy.length === 0 || !searchQuery) return []

  const tokens = searchQuery.toLowerCase().split(/\s+/)
  return options.filter((option) => {
    const matchesSearchBy = searchBy.some((key) => {
      if (key === 'tags') {
        const { tags } = option
        return tags?.some((tag) =>
          tokens.every((token) => tag.toLowerCase().includes(token))
        )
      }

      const value = option[key]

      if (typeof value === 'string') {
        const lowerCaseValue = value?.toLowerCase()
        return tokens.every((token) => lowerCaseValue?.includes(token))
      }
      return false
    })

    return matchesSearchBy
  })
}

/** Replaces dynamic metrics in a given markdown string.
 *
 * Replaces placeholders like `{{returns-FII-USD}}` and `{{george-USA}}`
 * with the actual values fetched from the Tontine API.
 */
export async function replaceDynamicMetrics(markdown: string): Promise<string> {
  const matchesReturn = [...markdown.matchAll(/returns-([A-Z]+)-([A-Z]+)/g)]
  const matchesGeorge = [...markdown.matchAll(/george-([A-Z]+)/g)]

  const devBuild =
    Boolean(isCypress) ||
    buildContext === 'development' ||
    buildContext === 'deploy_preview'

  let modifiedMarkdown = markdown

  if (matchesReturn.length > 0) {
    for (const match of matchesReturn) {
      const [placeholder, strategy, currency] = match

      if (devBuild) {
        modifiedMarkdown = modifiedMarkdown.replace(
          placeholder,
          `${CONSTANTS.TONTINE_BOL_RATE}`
        )
        continue
      }

      const returnRate =
        (await fetchReturnRate({
          currency: currency ?? 'USD',
          strategy: strategy ?? 'FII',
        })) ?? CONSTANTS.TONTINE_BOL_RATE
      modifiedMarkdown = modifiedMarkdown.replace(
        placeholder,
        returnRate.toFixed(2)
      )
    }
  }

  if (matchesGeorge.length > 0) {
    for (const match of matchesGeorge) {
      const [placeholder, georgeCurrency] = match

      if (devBuild) {
        modifiedMarkdown = modifiedMarkdown.replace(
          placeholder,
          `${CONSTANTS.ANNUAL_YIELD_RATE}`
        )
        continue
      }

      const georgeRate =
        (await fetchGeorgeYield(georgeCurrency ?? 'USA')) ??
        CONSTANTS.ANNUAL_YIELD_RATE

      modifiedMarkdown = modifiedMarkdown.replace(
        placeholder,
        georgeRate.toFixed(2)
      )
    }
  }

  return modifiedMarkdown
}

/**
 * Copies the given content to the clipboard.
 */
export const copyToClipboard = (
  content: string,
  onSuccess: () => void,
  onError: () => void
) => {
  navigator.clipboard.writeText(content).then(onSuccess).catch(onError)
}

export const isLoadedWindow = () => typeof window !== 'undefined'

/** Returns an array of pseudo breakpoints based on the given length.
 * - ['xl', 'lg'] if length is 3.
 * - ['lg'] if length is greater than 3.
 * - An empty array if length is less than 3.
 */
export const getArrayBasedOnLength = (
  length: number
): Array<BreakpointsType> => {
  if (length === 3) {
    return ['xl', 'lg', 'sm']
  }
  if (length > 3) {
    return ['lg']
  }
  return []
}

/**
 * Returns true if the given hostname is configured to track events without consent.
 */
export const shouldTrackWithoutConsent = (hostname: string): boolean => {
  return CONSTANTS.DOMAIN_TRACKING_CONFIG.some(
    (config) => config.domain === hostname && config.trackWithoutConsent
  )
}
