import { LaunchIcon } from '@sanity/icons'

import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { LocalizedContentParser } from '../../components/typography/LocalizedContentParser'
import { Title } from '../../components/typography/Title'
import { WrappedText } from '../../components/typography/WrappedText'
import strings from '../../data-resource/strings.json'
import { cn, isExternalLink } from '../../helper-functions/UtilFunctions'
import { STYLE } from '../../styles/style'
import { NavEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type { SubMenuItem } from '../../types/shared-page-data.types'

/** Renders a common sub-navigation item with an icon, title, and subtitle.
 * - Displays a 'New' or 'Coming Soon' badge if applicable.
 * - Highlights external links with an icon.
 */
function CommonSubNavItems({
  title,
  subtitle,
  icon,
  subMenuItemNew,
  subMenuItemComingSoon,
  subSlug,
}: Omit<
  SubMenuItem,
  | 'pageSlug'
  | 'sectionSlug'
  | 'customLink'
  | 'linkType'
  | 'customParamsButton'
  | 'customParams'
>) {
  const isExternal = isExternalLink(subSlug)
  const badgeClassName =
    'flex h-min w-min rounded-md px-1 py-0.5 text-center text-[clamp(0.5rem,0.4647rem+0.0941vw,0.7rem)] leading-none font-semibold'
  return (
    <BoxContainer className='group/child peer flex items-center gap-[0.625rem] p-[0.1rem]'>
      <BoxContainer className='flex flex-col items-center self-start'>
        <BoxContainer className='relative flex flex-col items-center gap-[0.1rem]'>
          <SanityImage
            src={icon?.url}
            alt={icon?.altText}
            skeletonProps={{ className: 'h-[2rem] w-[2rem]' }}
            className='flex grow'
            sizes={STYLE.SMALL_ICON_IMAGE_OPTIMIZATION}
            fillProp
          />

          {subMenuItemComingSoon && (
            <BoxContainer
              className={cn(
                badgeClassName,
                'bg-golden-yellow text-background-1000'
              )}
            >
              {strings.COMING_SOON}
            </BoxContainer>
          )}
          {subMenuItemNew && (
            <BoxContainer
              className={cn(
                badgeClassName,
                'mt-1 bg-jade py-0.5 text-[0.75rem] text-background-100 shadow-sm'
              )}
            >
              {strings.NEW_UPPERCASE}
            </BoxContainer>
          )}
        </BoxContainer>
      </BoxContainer>
      <BoxContainer className='flex w-[80%] flex-col self-start'>
        <Title
          title={
            <>
              <LocalizedContentParser renderDefaultBlock={false}>
                {title}
              </LocalizedContentParser>
              {isExternal && <LaunchIcon />}
            </>
          }
          className='flex w-max items-center font-semibold text-[1.05rem] text-fg transition-colors duration-100 ease-in-out group-hover/child:text-brand'
        />
        <WrappedText className='min-w-[16rem] text-[0.85rem] text-fg'>
          <LocalizedContentParser
            renderWrapper={false}
            renderDefaultBlock={false}
          >
            {subtitle}
          </LocalizedContentParser>
        </WrappedText>
      </BoxContainer>
    </BoxContainer>
  )
}

/**
 * DesktopSubNav is used to display the sub menu items from each navigation item on hover event
 */
export function DesktopSubNav({
  title,
  subtitle,
  subSlug,
  icon,
  subMenuItemNew,
  subMenuItemComingSoon,
  subMenuItemDisabled,
}: SubMenuItem) {
  return (
    <BoxContainer
      as={'li'}
      className='group h-full max-h-[5rem] max-w-[50rem] aria-disabled:opacity-60'
      role='listitem'
      disabled={subMenuItemDisabled}
    >
      {subMenuItemDisabled ? (
        <CommonSubNavItems
          title={title}
          subtitle={subtitle}
          icon={icon}
          subMenuItemNew={subMenuItemNew}
          subMenuItemComingSoon={subMenuItemComingSoon}
          subSlug={subSlug}
        />
      ) : (
        <NextLink
          href={subSlug}
          customEvent={NavEvent.item_clicked}
          hideExternalIcon
          objectId={'nav_bar_sub_link'}
        >
          <CommonSubNavItems
            title={title}
            subtitle={subtitle}
            icon={icon}
            subMenuItemNew={subMenuItemNew}
            subMenuItemComingSoon={subMenuItemComingSoon}
            subSlug={subSlug}
          />
        </NextLink>
      )}
    </BoxContainer>
  )
}
