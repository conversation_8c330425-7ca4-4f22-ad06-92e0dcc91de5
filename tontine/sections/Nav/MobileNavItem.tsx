'use client'

import { ChevronDownIcon } from '@sanity/icons'

import { BoxContainer } from '../../components/common/BoxContainer'
import { NextLink } from '../../components/common/NextLink'
import { SanityImage } from '../../components/common/SanityImage'
import { Title } from '../../components/typography/Title'
import { WrappedText } from '../../components/typography/WrappedText'
import { Divider } from '../../components/ui/Divider'
import { Collapsible } from '../../components/ui/collapse/Collapse'
import strings from '../../data-resource/strings.json'
import { cn, generateSlug } from '../../helper-functions/UtilFunctions'
import { NavEvent } from '../../types/Analytics/AnalyticsEvents.types'
import type {
  NavBarNavigationMenuItem,
  SubMenuItem,
} from '../../types/shared-page-data.types'

const subSubMenuItems = cn('flex items-center justify-between')

const tagBaseStyle = cn(
  'flex items-center justify-center gap-0.5 rounded-full px-2 py-0.5 font-semibold text-[clamp(0.8rem,0.4647rem+0.0941vw,0.8rem)]'
)

/** CommonSubMenuContent is a component used to render a sub menu item content
 * for both navigation menu items and footer menu items.
 */
const CommonSubMenuContent = ({
  item,
  onToggle,
}: {
  item: SubMenuItem
  onToggle?: () => void
}) => {
  return (
    <>
      <BoxContainer
        onClick={onToggle}
        className='flex items-center gap-3 pl-3 text-4 hover:text-fg md:hover:text-brand lg:text-4.5'
      >
        <SanityImage
          alt={item?.icon?.altText}
          src={item?.icon?.url}
          skeletonProps={{ className: 'w-[25px] h-[25px] rounded-sm' }}
          fillProp
        />
        <Title>{item?.title}</Title>
      </BoxContainer>
      {item?.subMenuItemNew && (
        <BoxContainer
          className={cn(
            tagBaseStyle,
            'w-24 bg-jade text-background-100 xl:w-auto'
          )}
        >
          <Divider variant='solid' className='min-w-3' />
          <WrappedText>{strings.NEW_UPPERCASE}</WrappedText>
          <Divider variant='solid' className='min-w-3' />
        </BoxContainer>
      )}
      {item.subMenuItemComingSoon && (
        <BoxContainer
          className={cn(
            tagBaseStyle,
            'w-24 bg-golden-yellow text-background-1000 xl:w-auto'
          )}
        >
          {strings.COMING_SOON}
        </BoxContainer>
      )}
    </>
  )
}

/**
 * `MobileNavItem` is used to show the sub menu items form navigation items on phone-sized screens.
 */
export const MobileNavItem = ({
  stringTitle,
  navItemSlug,
  navigationSubMenuItems,
  onToggle,
}: NavBarNavigationMenuItem) => {
  const itemHasSubItems: boolean = Boolean(
    navigationSubMenuItems && navigationSubMenuItems?.length > 0
  )

  const NavItemTrigger = () => (
    <NextLink
      href={navItemSlug}
      objectId={'mobile_nav_bar_main_link'}
      customEvent={NavEvent.item_clicked}
      onClick={onToggle}
    >
      <Title className='flex items-center gap-3 px-6 font-semibold text-fg-700 text-lg group-hover:text-fg md:hover:text-brand'>
        {stringTitle}
      </Title>
    </NextLink>
  )

  return (
    <>
      {itemHasSubItems ? (
        <Collapsible
          triggerProps={{
            children: (
              <>
                <NavItemTrigger />
                {itemHasSubItems && (
                  <ChevronDownIcon className='text-2xl group-aria-expanded/trigger:rotate-180' />
                )}
              </>
            ),
            className: 'flex items-center justify-between pr-6',
          }}
          title={stringTitle}
          contentProps={{
            className: 'relative',
            contentInnerProps: {
              className: 'flex flex-col gap-3 p-3',
              role: 'list',
            },
          }}
        >
          {navigationSubMenuItems?.map((item: SubMenuItem, index) => {
            const subSlug = generateSlug({
              pageSlug: item?.pageSlug,
              customLink: item?.customLink,
              sectionSlug: item?.sectionSlug,
              linkType: item?.linkType,
              customParamsButton: item?.customParamsButton,
              customParams: item?.customParams,
            })
            return item?.subMenuItemDisabled ? (
              <BoxContainer
                key={`coming-soon-mobile-item-${subSlug}-${index}`}
                className={cn(
                  subSubMenuItems,
                  'pointer-events-none opacity-65'
                )}
              >
                <CommonSubMenuContent item={item} />
              </BoxContainer>
            ) : (
              <NextLink
                href={subSlug}
                className='subSubMenuItems flex w-full items-center justify-between'
                customEvent={NavEvent.item_clicked}
                objectId={'mobile_nav_bar_sub_link'}
                key={`nav-mobile-item-${subSlug}-${index}`}
              >
                <CommonSubMenuContent onToggle={onToggle} item={item} />
              </NextLink>
            )
          })}
        </Collapsible>
      ) : (
        <NavItemTrigger />
      )}
    </>
  )
}
