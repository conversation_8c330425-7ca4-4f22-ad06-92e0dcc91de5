import { type NextRequest, NextResponse, userAgent } from 'next/server'

import { CONSTANTS } from './data-resource/constants'
import { SECURITY_CONSTANTS } from './data-resource/security-constants'
import { logServerless } from './serverless/ApiUtilFunctions'
import { isCypress, isProd, websiteDomain } from './serverless/keys'

// Define your supported locales
const locales = CONSTANTS.LANGUAGES
const defaultLocale = 'en'

// Get the preferred locale from the request without external libraries
function getLocale(request: NextRequest): string {
  // Get Accept-Language header
  const acceptLanguage = request.headers.get('accept-language') || ''
  // Parse the Accept-Language header
  // Format is typically: "en-US,en;q=0.9,es;q=0.8,de;q=0.7"
  const userLocales = acceptLanguage
    .split(',')
    .map((item) => {
      // Split "en-US;q=0.9" into language and quality
      const [locale, qualityStr] = item.trim().split(';q=')
      // Get base language from "en-US" -> "en"
      const language = locale?.split('-')[0]
      // Parse quality or default to 1
      const quality = qualityStr ? Number.parseFloat(qualityStr) : 1.0

      return { language, quality }
    })
    // Sort by quality (highest first)
    .sort((a, b) => b.quality - a.quality)
    // Extract just the language codes
    .map((item) => item.language)

  return userLocales?.[0] || defaultLocale
}

/** Set security-related headers. */
function setSecurityHeaders({
  headers,
  nonce,
  cspHeader,
}: {
  headers: Headers
  nonce: string
  cspHeader: string
}): void {
  headers.set('X-Frame-Options', 'SAMEORIGIN')
  headers.set('Content-Security-Policy', cspHeader)
  headers.set('x-nonce', nonce)
}

/** Generate the Content Security Policy header value. */
function generateCspHeader({ nonce }: { nonce: string }): string {
  return `
    default-src 'self';
    script-src 'nonce-${nonce}' 'strict-dynamic' 'unsafe-eval' 'unsafe-inline' http: https: https://consent.cookiebot.com https://consent.cookiebot.eu https://consentcdn.cookiebot.com;
    style-src 'self' 'unsafe-inline';
    connect-src 'self' ${SECURITY_CONSTANTS.ALLOWED_CONNECT_SOURCES};
    img-src 'self' blob: data: ${SECURITY_CONSTANTS.ALLOWED_MEDIA_SOURCES};
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    media-src 'self' data: blob: ${SECURITY_CONSTANTS.ALLOWED_MEDIA_SOURCES};
    form-action 'self';
    frame-ancestors 'self' ${SECURITY_CONSTANTS.ALLOWED_FRAME_ANCESTORS};
    frame-src 'self' ${SECURITY_CONSTANTS.ALLOWED_FRAME_SRC_SOURCES};
    upgrade-insecure-requests;
    script-src-elem 'self' 'unsafe-inline' ${SECURITY_CONSTANTS.ALLOWED_SCRIPT_SOURCES};
    script-src-attr 'self' 'unsafe-inline' ${SECURITY_CONSTANTS.ALLOWED_SCRIPT_SOURCES};
  `
    .replace(/\s{2,}/g, ' ')
    .trim()
}

/** Middleware function that handles requests and modifies responses. */
export function middleware(request: NextRequest) {
  // Check if there is any supported locale in the pathname
  const pathname = request.nextUrl.pathname

  // Check if path starts with a locale // TODO: Revert when ready
  const matchLocale = locales.find(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )

  if (isProd) {
    // In production, redirect non-English locales to root
    if (matchLocale && matchLocale !== 'en') {
      const newPath = pathname.replace(`/${matchLocale}`, '')
      return NextResponse.redirect(new URL(newPath || '/', request.url))
    }

    // Rewrite paths without locale to /en
    if (!matchLocale) {
      return NextResponse.rewrite(
        new URL(
          `/en${pathname.startsWith('/') ? '' : '/'}${pathname}`,
          request.url
        )
      )
    }
  } else {
    // In development, handle locales normally
    if (!matchLocale) {
      const locale = getLocale(request)
      return NextResponse.rewrite(
        new URL(
          `/${locale}${pathname.startsWith('/') ? '' : '/'}${pathname}`,
          request.url
        )
      )
    }
  }

  if (isCypress) return NextResponse.next()

  const isPdfRequest = request.url.includes('.pdf')
  if (isPdfRequest) {
    // Set the canonical LINK tag for the PDF file
    const canonicalUrl = `${websiteDomain}${request.nextUrl.pathname}`

    const headers = new Headers(request.headers)

    const response = NextResponse.next({
      request: {
        headers,
      },
    })

    response.headers.set('Link', `<${canonicalUrl}>; rel="canonical"`)

    // Return the modified response with the canonical URL set
    return response
  }

  const nonce = Buffer.from(crypto.randomUUID()).toString('base64')

  const cspHeader = generateCspHeader({ nonce })

  const requestHeaders = new Headers(request.headers)

  if (!request.url.includes('assets') && !isCypress && isProd) {
    const { ua, isBot } = userAgent(request)
    const ip = request.headers.get('X-Forwarded-For')
    logServerless({
      message: `
    URL: ${request.url}
    IP: ${ip}
    UA: ${ua}
    IS_BOT: ${isBot}
    `,
      logLevel: 'info',
    })
  }

  setSecurityHeaders({ headers: requestHeaders, nonce, cspHeader })

  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  })

  setSecurityHeaders({ headers: response.headers, nonce, cspHeader })

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico, sitemap.xml, robots.txt (metadata files)
     */
    {
      source:
        '/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|assets).*)',
      missing: [
        { type: 'header', key: 'next-router-prefetch' },
        { type: 'header', key: 'purpose', value: 'prefetch' },
      ],
    },

    {
      source:
        '/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|assets).*)',
      has: [
        { type: 'header', key: 'next-router-prefetch' },
        { type: 'header', key: 'purpose', value: 'prefetch' },
      ],
    },

    {
      source:
        '/((?!api|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt|assets).*)',
      has: [{ type: 'header', key: 'x-present' }],
      missing: [{ type: 'header', key: 'x-missing', value: 'prefetch' }],
    },
  ],
}
