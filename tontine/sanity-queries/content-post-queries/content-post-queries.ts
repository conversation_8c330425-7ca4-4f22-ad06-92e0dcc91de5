import {
  localizedPortableTextQuery,
  sharedSectionDataQuery,
} from '../common-type-queries/common-types-queries'

/**
 * Fetches all the content posts data from sanity
 */
const contentPostQuery = /* groq */ `
${sharedSectionDataQuery}
defined(_type)=>{_type},
defined(_createdAt)=>{_createdAt},
defined(_updatedAt)=>{_updatedAt},
"seoKeywords": keywords[$lang],
defined(authorArray)=>{"authors": array::compact(authorArray[]->{personName, "id": _id, personSocialLink})},
defined(postImage)=>{
  "postImage":{
      "url": postImage.asset->url,
      "altText": postImage.asset->altText,
  },
},
defined(localizedBody)=>{"body":  localizedBody {${localizedPortableTextQuery}}},
defined(publishDate)=>{publishDate},
defined(readingTime)=>{readingTime},
defined(eiisVisibility)=>{eiisVisibility},
defined(seoKeywords)=>{seoKeywords},
defined(thumbnail)=>{
  "videoThumbnail":{
    "url": thumbnail.asset->url,
    "altText": thumbnail.asset->altText,
  },
},
defined(videoFile)=>{
  "videoFile": videoFile.asset->{
    defined(playbackId)=>{"playbackId": playbackId},
    defined(data.duration)=>{"duration": data.duration},
    defined(data.max_stored_resolution)=>{"quality": data.max_stored_resolution},
    defined(data.tracks)=>{"width": data.tracks[max_width != null].max_width},
    defined(data.tracks)=>{"height": data.tracks[max_width != null].max_height},
    defined(_updatedAt)=>{"updatedAt": _updatedAt},
    defined(_createdAt)=>{"createdAt": _createdAt},
  },
},

defined(manuscript)=>{
  manuscript{
    asset->{
      url,
      originalFilename,
    },
  },
},
`

/**
 * Fetches 3 related posts from sanity excluding the current post
 */
const moreLikeThisQuery = /* groq */ `
*[_type == $postType && slug.current != $slug && count(*[references(^._id)]) > 0] | order(_createdAt desc) [0...3] {
  ${sharedSectionDataQuery}
  defined(postImage)=>{
    "postImage":{
      "url": postImage.asset->url,
      "altText": postImage.asset->altText,
    },
  },
  defined(publishDate)=>{publishDate},
  defined(readingTime)=>{readingTime},
  defined(videoFile)=>{
    "videoFile": {
      "duration": videoFile.asset->data.duration
  }
},
  "isVideoFile": defined(videoFile),
}
`

const contentPostData = /* groq */ `
{
  defined(manuscript) => {
    "slug": slug,
    manuscript {
      asset->{
        url,
        originalFilename,
      },
    },
  },
  defined(videoFile) => {
    "slug": slug,
    title,
    subtitle,
    "videoThumbnail": coalesce(thumbnail.asset->url, postImage.asset->url),
    ...videoFile.asset->{
      defined(playbackId) => {"playbackId": playbackId},
      defined(data.duration) => {"duration": data.duration},
    },
  }
}
`

const contentPostWriteDataQuery = /* groq */ `
*[_id == $WEBSITE_TONTINE][0]{
  pagesOnWebsite[]->{
    "postsArray": array::compact(
      pageSections[]->{
        "combinedPosts": [
          ...postsArray[]->${contentPostData},
          ...featuredPost[]->${contentPostData},
        ]
      }.combinedPosts[]
    )
  },
}
`

export { contentPostQuery, contentPostWriteDataQuery, moreLikeThisQuery }
