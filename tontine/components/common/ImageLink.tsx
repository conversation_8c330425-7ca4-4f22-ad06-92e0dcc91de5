import { cn, convertToPlainText } from '../../helper-functions/UtilFunctions'
import type { LinkWithImageProps } from '../../types/components/NextLink.types'
import { NextLink } from './NextLink'
import { SanityImage } from './SanityImage'

/** `ImageLink` is a component that displays an image wrapped within a link.
 * It uses the `NextLink` component to render the link and the `SanityImage`
 * component to render the image.
 */
export const ImageLink = ({
  href,
  linkImage,
  linkTitle,
  customEvent,
  hideExternalIcon,
  parentSectionId,
  imageProps,
  linkProps,
}: LinkWithImageProps) => {
  return (
    <>
      <NextLink
        href={href}
        objectId={parentSectionId}
        hideExternalIcon={hideExternalIcon}
        customEvent={customEvent}
        customValue={linkTitle}
        {...linkProps}
        className={cn('duration-200 lg:hover:opacity-80', linkProps?.className)}
        aria-label={convertToPlainText({ value: linkTitle })}
      >
        <SanityImage
          src={linkImage?.url}
          alt={linkImage?.altText}
          fillProp
          {...imageProps}
          skeletonProps={{
            className: cn(
              'z-100 h-12 w-36',
              imageProps?.skeletonProps?.className
            ),
          }}
          sizes={'9rem'}
        />
      </NextLink>
    </>
  )
}
