import './styles.css'

import { <PERSON><PERSON>, Flex } from '@sanity/ui'
import { useEffect } from 'react'
import { ToolLink, type ToolMenuProps } from 'sanity'

import { SanityGuide } from './SanityGuide'

const customTool = {
  name: 'guide',
  title: 'Guide',

  component: SanityGuide,
}

/**
 * Component that displays the custom tool menu in the navbar
 */
export function CustomToolMenu(props: ToolMenuProps) {
  const { activeToolName, context, tools } = props
  const isSidebar = context === 'sidebar'

  const structute = props.tools[0]
  console.log(structute.router?.getBasePath())

  // Change flex direction depending on context
  const direction = isSidebar ? 'column' : 'row'

  // Add a custom tool to the list of tools

  // biome-ignore lint/correctness/useExhaustiveDependencies: Will cause unnecessary re-renders
  useEffect(() => {
    const toolGuide = tools.every((e) => e.name !== customTool.name)
    if (!toolGuide) return
    tools.push(customTool)
  }, [])
  return (
    <Flex gap={1} direction={direction}>
      {tools.map((tool) => (
        <Button
          as={ToolLink}
          key={tool.name}
          name={tool.name}
          padding={3}
          selected={tool.name === activeToolName}
          text={tool.title || tool.name}
          className={`custom-button ${tool.name === activeToolName && 'custom-button_active'}`}
        />
      ))}
    </Flex>
  )
}
